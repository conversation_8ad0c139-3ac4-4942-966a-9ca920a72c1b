# APITable View System Documentation

## Table of Contents
1. [View Architecture Overview](#view-architecture-overview)
2. [View Types and Components](#view-types-and-components)
3. [View Data Flow](#view-data-flow)
4. [View Configuration](#view-configuration)
5. [View State Management](#view-state-management)
6. [API Endpoints](#api-endpoints)
7. [Code Examples](#code-examples)
8. [File Structure](#file-structure)

## 1. View Architecture Overview

The APITable view system is built on a modular architecture that supports multiple view types for displaying and interacting with data. The system follows a Model-View-Controller pattern with clear separation of concerns:

### Core Architecture Components

- **View Models**: Abstract base classes and specific implementations for each view type
- **View Components**: React components that render the UI for each view
- **View Derivation**: Computed data processing layer that transforms raw data for view consumption
- **State Management**: Redux-based state management with middleware for view derivations
- **Backend Services**: Java-based REST API services for view operations

### Key Design Principles

1. **Extensibility**: New view types can be easily added by extending base classes
2. **Performance**: View derivations are computed and cached for optimal rendering
3. **Consistency**: All views share common interfaces and patterns
4. **Flexibility**: Views support extensive configuration options

## 2. View Types and Components

APITable supports 7 main view types, each optimized for different data visualization and interaction patterns:

### 2.1 Grid View (ViewType.Grid = 1)
- **Purpose**: Traditional spreadsheet-like tabular data display
- **Features**: 
  - Sortable and filterable columns
  - Resizable column widths
  - Frozen columns support
  - Row height customization (Short, Medium, Tall, ExtraTall)
  - Statistical aggregations per column

### 2.2 Kanban View (ViewType.Kanban = 2)
- **Purpose**: Card-based workflow management
- **Features**:
  - Grouping by single-select or multi-select fields
  - Drag-and-drop card movement
  - Cover image support
  - Hidden group management
  - Card styling options

### 2.3 Gallery View (ViewType.Gallery = 3)
- **Purpose**: Visual card-based data presentation
- **Features**:
  - Image cover support with fit options
  - Customizable card layouts
  - Filtering and sorting capabilities
  - Responsive grid layout

### 2.4 Form View (ViewType.Form = 4)
- **Purpose**: Data entry and record creation
- **Features**:
  - Custom form layouts
  - Field validation
  - Conditional field visibility
  - Submission handling

### 2.5 Calendar View (ViewType.Calendar = 5)
- **Purpose**: Time-based data visualization
- **Features**:
  - Date field mapping
  - Multiple calendar layouts (month, week, day)
  - Event styling and coloring
  - Date range filtering

### 2.6 Gantt View (ViewType.Gantt = 6)
- **Purpose**: Project timeline and dependency management
- **Features**:
  - Task timeline visualization
  - Dependency relationships
  - Progress tracking
  - Resource allocation

### 2.7 Organization Chart View (ViewType.OrgChart = 7)
- **Purpose**: Hierarchical relationship visualization
- **Features**:
  - Tree-like structure display
  - Parent-child relationships
  - Node styling options
  - Interactive navigation

## 3. View Data Flow

The view system implements a sophisticated data flow architecture:

### 3.1 Data Processing Pipeline

```
Raw Data → View Derivation → Computed View Data → UI Rendering
```

1. **Raw Data**: Stored in datasheet snapshots with records and fields
2. **View Derivation**: Middleware processes data based on view configuration
3. **Computed View Data**: Optimized data structure for specific view type
4. **UI Rendering**: React components render the final view

### 3.2 View Derivation System

Each view type has a corresponding derivation class:

- `ViewDerivateGrid`: Processes tabular data with sorting, filtering, grouping
- `ViewDerivateKanban`: Groups records by field values into columns
- `ViewDerivateGallery`: Prepares card-based data with image processing
- `ViewDerivateCalendar`: Maps records to calendar events by date fields
- `ViewDerivateGantt`: Processes timeline and dependency data
- `ViewDerivateOrgChart`: Builds hierarchical tree structures

### 3.3 Data Flow Triggers

View derivations are recalculated when:
- View configuration changes (filters, sorting, grouping)
- Underlying data changes (record updates, field modifications)
- View switching occurs
- Search keywords are applied

## 4. View Configuration

Views are highly configurable through their property interfaces:

### 4.1 Base View Properties

All views inherit from `IViewPropertyBase`:

```typescript
interface IViewPropertyBase {
  id: string;                    // Unique view identifier
  name: string;                  // Display name
  type: ViewType;               // View type enum
  rows: IViewRow[];             // Row configuration
  columns: IViewColumn[];       // Column configuration
  autoSave?: boolean;           // Auto-save behavior
  description?: string;         // View description
  hidden?: boolean;             // Visibility flag
  filterInfo?: IFilterInfo;     // Filter configuration
  sortInfo?: ISortInfo;         // Sort configuration
  lockInfo?: IViewLockInfo;     // Lock settings
}
```

### 4.2 View-Specific Properties

Each view type extends the base with specific properties:

#### Grid View Properties
```typescript
interface IGridViewProperty extends IViewPropertyBase {
  type: ViewType.Grid;
  rowHeightLevel: RowHeightLevel;
  frozenColumnCount: number;
  displayHiddenColumnWithinMirror?: boolean;
}
```

#### Kanban View Properties
```typescript
interface IKanbanViewProperty extends IViewPropertyBase {
  type: ViewType.Kanban;
  groupInfo?: IGroupInfo;
  style: IKanbanStyle;
}
```

### 4.3 Column Configuration

Columns can be customized per view type:

```typescript
interface IGridViewColumn extends IViewColumn {
  width?: number;           // Column width in pixels
  statType?: StatType;      // Statistical aggregation type
}
```

## 5. View State Management

The view system uses Redux for state management with specialized middleware:

### 5.1 Redux State Structure

```typescript
interface IReduxState {
  datasheetMap: IDatasheetMap;    // Contains view data
  pageParams: IPageParams;        // Current view/datasheet context
  // ... other state slices
}
```

### 5.2 View Derivation Middleware

The `viewDerivationMiddleware` automatically:
- Detects state changes that affect views
- Triggers view derivation recalculation
- Updates computed view data in the store
- Optimizes performance through selective updates

### 5.3 Key Selectors

Important selectors for view data access:

```typescript
// Get current active view
Selectors.getCurrentView(state)

// Get visible rows for current view
Selectors.getVisibleRows(state)

// Get view derivation data
Selectors.getViewDerivation(state, datasheetId, viewId)

// Get field map for view
Selectors.getFieldMap(state, datasheetId)
```

## 6. API Endpoints

The backend provides REST APIs for view operations:

### 6.1 View Management Endpoints

- `GET /api/v1/spaces/{spaceId}/datasheets/{datasheetId}/views` - List views
- `POST /api/v1/spaces/{spaceId}/datasheets/{datasheetId}/views` - Create view
- `PUT /api/v1/spaces/{spaceId}/datasheets/{datasheetId}/views/{viewId}` - Update view
- `DELETE /api/v1/spaces/{spaceId}/datasheets/{datasheetId}/views/{viewId}` - Delete view

### 6.2 View Data Endpoints

- `GET /api/v1/spaces/{spaceId}/datasheets/{datasheetId}/views/{viewId}/records` - Get view records
- `GET /api/v1/spaces/{spaceId}/datasheets/{datasheetId}/views/{viewId}/meta` - Get view metadata

### 6.3 Backend View Type Enum

```java
public enum ViewType {
    NOT_SUPPORT(0),
    GRID(1),
    KANBAN(2),
    GALLERY(3),
    FORM(4),
    CALENDAR(5),
    GANTT(6);
}
```

## 7. Code Examples

### 7.1 Creating a New View

```typescript
// Frontend view creation
const newGridView = GridView.generateDefaultProperty(snapshot, activeViewId);

// Dispatch creation action
dispatch(StoreActions.addView(datasheetId, newGridView));
```

### 7.2 View Component Rendering

```typescript
// Main view component switch
switch (currentView.type) {
  case ViewType.Grid:
    return <KonvaGridView width={width} height={height} />;
  case ViewType.Gallery:
    return <GalleryView height={height} width={width} />;
  case ViewType.Calendar:
    return <CalendarView height={height} width={width} />;
  case ViewType.Kanban:
    return <KanbanView height={height} width={width} />;
  // ... other view types
}
```

### 7.3 View Derivation Factory

```typescript
class ViewDerivateFactory {
  static createViewDerivate(state: IReduxState, datasheetId: string, viewType: ViewType): ViewDerivateBase {
    switch(viewType) {
      case ViewType.Calendar:
        return new ViewDerivateCalendar(state, datasheetId);
      case ViewType.Gallery:
        return new ViewDerivateGallery(state, datasheetId);
      case ViewType.Grid:
        return new ViewDerivateGrid(state, datasheetId);
      case ViewType.Kanban:
        return new ViewDerivateKanban(state, datasheetId);
      case ViewType.Gantt:
        return new ViewDerivateGantt(state, datasheetId);
      case ViewType.OrgChart:
        return new ViewDerivateOrgChart(state, datasheetId);
      case ViewType.NotSupport:
        return new ViewDerivateGrid(state, datasheetId); // Fallback to grid
      default:
        return new ViewDerivateGrid(state, datasheetId);
    }
  }
}
```

### 7.4 View State Selection

```typescript
// Using React hooks to select view data
const { currentView, rows, fieldMap } = useAppSelector((state: IReduxState) => {
  const currentView = Selectors.getCurrentView(state)!;
  const fieldMap = Selectors.getFieldMap(state, state.pageParams.datasheetId!)!;
  return {
    rows: Selectors.getVisibleRows(state),
    linearRows: Selectors.getLinearRows(state),
    currentView,
    fieldMap,
  };
}, shallowEqual);
```

### 7.5 View Configuration Updates

```typescript
// Update view filter
const updateViewFilter = (viewId: string, filterInfo: IFilterInfo) => {
  const action = StoreActions.setViewFilter(datasheetId, viewId, filterInfo);
  dispatch(action);
};

// Update view sorting
const updateViewSort = (viewId: string, sortInfo: ISortInfo) => {
  const action = StoreActions.setViewSort(datasheetId, viewId, sortInfo);
  dispatch(action);
};

// Switch active view
const switchToView = (viewId: string) => {
  dispatch(StoreActions.setActiveView(datasheetId, viewId));
};
```

### 7.6 Custom View Implementation

```typescript
// Example: Creating a custom view type
export class CustomView extends View {
  override get recordShowName() {
    return t(Strings.custom_record);
  }

  static generateDefaultProperty(snapshot: ISnapshot, activeViewId: string): ICustomViewProperty {
    const srcView = View.getSrcView(snapshot, activeViewId);

    return {
      id: DatasheetActions.getNewViewId(snapshot.meta.views),
      name: DatasheetActions.getDefaultViewName(snapshot.meta.views, ViewType.Custom),
      type: ViewType.Custom,
      columns: srcView.columns.map(col => ({ fieldId: col.fieldId })),
      rows: this.defaultRows(srcView),
      customConfig: {
        // Custom view specific configuration
        displayMode: 'cards',
        itemsPerPage: 20
      }
    };
  }
}
```

### 7.7 View Component Integration

```typescript
// Main view container with auto-sizing
<AutoSizer className="viewContainer" style={{ width: '100%', height: '100%' }}>
  {({ height, width }) => {
    switch (currentView.type) {
      case ViewType.Grid: {
        if (isMobile) {
          return <MobileGrid width={width} height={height - 40} />;
        }
        return <KonvaGridView width={width} height={height} />;
      }
      case ViewType.Gallery:
        return <GalleryView height={height} width={width} />;
      case ViewType.Calendar:
        return <CalendarView height={height} width={width} />;
      case ViewType.Kanban:
        return <KanbanView height={height} width={width} />;
      case ViewType.Gantt:
        return <GanttView width={width} height={height} />;
      case ViewType.OrgChart:
        return <OrgChartView width={width} height={height - (isMobile ? 40 : 0)} isMobile={isMobile} />;
      default:
        return <KonvaGridView width={width} height={height} />;
    }
  }}
</AutoSizer>
```

### 7.8 Backend View Service Example

```java
@Service
public class ViewService {

    @Autowired
    private ViewMapper viewMapper;

    public List<ViewInfo> getViewsByDatasheetId(String datasheetId) {
        return viewMapper.selectViewsByDatasheetId(datasheetId);
    }

    public ViewInfo createView(String datasheetId, CreateViewRequest request) {
        // Validate view configuration
        validateViewConfig(request);

        // Create view entity
        ViewEntity viewEntity = new ViewEntity();
        viewEntity.setDatasheetId(datasheetId);
        viewEntity.setName(request.getName());
        viewEntity.setType(request.getType());
        viewEntity.setMeta(JSON.toJSONString(request.getMeta()));

        // Save to database
        viewMapper.insert(viewEntity);

        return convertToViewInfo(viewEntity);
    }

    private void validateViewConfig(CreateViewRequest request) {
        ViewType viewType = ViewType.of(request.getType());
        if (viewType == null) {
            throw new BusinessException("Invalid view type");
        }

        // Additional validation based on view type
        switch (viewType) {
            case KANBAN:
                validateKanbanConfig(request.getMeta());
                break;
            case CALENDAR:
                validateCalendarConfig(request.getMeta());
                break;
            // ... other validations
        }
    }
}
```

## 8. File Structure

### 8.1 Frontend Core Files

```
packages/core/src/
├── model/views/                    # View model implementations
│   ├── index.ts                   # View factory and exports
│   ├── views.ts                   # Base View class
│   ├── grid_view.ts              # Grid view implementation
│   ├── kanban_view.ts            # Kanban view implementation
│   ├── gallery_view.ts           # Gallery view implementation
│   ├── calendar_view.ts          # Calendar view implementation
│   ├── gantt_view.ts             # Gantt view implementation
│   ├── form_view.ts              # Form view implementation
│   └── org_chart_view.ts         # Organization chart implementation
├── compute_manager/view_derivate/ # View derivation system
│   ├── factory.ts                # Derivation factory
│   ├── view_derivate_base.ts     # Base derivation class
│   ├── view_derivate_grid.ts     # Grid derivation
│   ├── view_derivate_kanban.ts   # Kanban derivation
│   └── ...                       # Other derivation classes
└── modules/shared/store/constants.ts # ViewType enum definition
```

### 8.2 Frontend UI Components

```
packages/datasheet/src/pc/components/
├── view/                          # Main view container
│   └── view.tsx                  # View routing component
├── konva_grid/                   # Grid view implementation
├── kanban_view/                  # Kanban view implementation
├── gallery_view/                 # Gallery view implementation
├── calendar_view/                # Calendar view implementation
├── gantt_view/                   # Gantt view implementation
└── org_chart_view/               # Organization chart implementation
```

### 8.3 Backend Files

```
backend-server/application/src/main/java/com/apitable/workspace/
├── enums/ViewType.java           # Backend view type enum
├── controller/                   # REST API controllers
├── service/                      # Business logic services
├── entity/                       # Database entities
├── dto/                          # Data transfer objects
└── vo/                           # View objects for API responses
```

## 9. Advanced View Configuration

### 9.1 Filtering System

Views support complex filtering through the `IFilterInfo` interface:

```typescript
interface IFilterInfo {
  conjunction: FilterConjunction;  // AND/OR logic
  conditions: IFilterCondition[];  // Individual filter conditions
}

interface IFilterCondition {
  fieldId: string;                // Target field ID
  operator: FilterOperator;       // Comparison operator
  value: any;                     // Filter value
  conditionId: string;           // Unique condition ID
}
```

**Supported Filter Operators:**
- Text fields: `Contains`, `DoesNotContain`, `Is`, `IsNot`, `IsEmpty`, `IsNotEmpty`
- Number fields: `Equal`, `NotEqual`, `GreaterThan`, `LessThan`, `GreaterThanOrEqual`, `LessThanOrEqual`
- Date fields: `Is`, `IsBefore`, `IsAfter`, `IsOnOrBefore`, `IsOnOrAfter`, `IsWithin`
- Select fields: `Is`, `IsNot`, `Contains`, `DoesNotContain`

### 9.2 Sorting Configuration

Multi-level sorting is supported through `ISortInfo`:

```typescript
interface ISortInfo {
  rules: ISortedField[];          // Sort rules in priority order
  keepSort: boolean;              // Maintain sort when adding records
}

interface ISortedField {
  fieldId: string;                // Field to sort by
  desc: boolean;                  // Descending order flag
}
```

### 9.3 Grouping System

Views can group records by field values:

```typescript
interface IGroupInfo {
  fieldId: string;                // Grouping field
  desc: boolean;                  // Group order
}
```

**Grouping Support by View Type:**
- **Kanban**: Single field grouping (required)
- **Gallery**: Optional single field grouping
- **Grid**: Multiple level grouping support
- **Calendar**: Automatic date-based grouping

### 9.4 View Styling Options

Each view type supports specific styling configurations:

#### Kanban Style Configuration
```typescript
interface IKanbanStyle {
  kanbanFieldId: string;          // Grouping field ID
  coverFieldId?: string;          // Cover image field
  isCoverFit?: boolean;          // Cover image fit mode
  isColNameVisible?: boolean;     // Show column names
  hiddenGroupMap?: HiddenGroupMap; // Hidden groups
}
```

#### Gallery Style Configuration
```typescript
interface IGalleryViewStyle {
  coverFieldId?: string;          // Cover image field
  isCoverFit?: boolean;          // Cover image fit mode
  isColNameVisible?: boolean;     // Show field names
  cardCount?: number;            // Cards per row
}
```

## 10. View Permissions and Security

### 10.1 View-Level Permissions

Views inherit permissions from their parent datasheet but can have additional restrictions:

```typescript
interface IViewLockInfo {
  lockedFields?: string[];        // Fields that cannot be modified
  lockedRows?: string[];          // Rows that cannot be modified
}
```

### 10.2 Field Permissions in Views

Individual fields can have different permission levels within views:

```typescript
interface IFieldPermission {
  fieldId: string;
  role: FieldRole;                // None, Reader, Editor
  setting: IFieldRoleSetting;
}

enum FieldRole {
  None = 'none',                  // Field hidden
  Reader = 'reader',              // Read-only access
  Editor = 'editor'               // Full edit access
}
```

### 10.3 View Sharing and Collaboration

Views can be shared with specific permissions:

- **View-only**: Users can see data but cannot modify
- **Edit**: Users can modify data within view constraints
- **Manage**: Users can modify view configuration

## 11. Performance Optimization

### 11.1 View Derivation Caching

The system implements intelligent caching for view derivations:

```typescript
// Cached derivation structure
interface IViewDerivation {
  viewId: string;
  visibleRows: IViewRow[];        // Filtered and sorted rows
  groupBreakpoint?: number[];     // Group boundaries
  linearRows?: IViewRow[];        // Flattened row structure
}
```

**Cache Invalidation Triggers:**
- Record data changes
- View configuration updates
- Field schema modifications
- Permission changes

### 11.2 Lazy Loading Strategies

Large datasets are handled through:

- **Virtualized Rendering**: Only visible rows are rendered
- **Progressive Loading**: Data loaded in chunks as needed
- **Background Updates**: Non-critical updates processed asynchronously

### 11.3 Memory Management

The system optimizes memory usage through:

- **Selective State Updates**: Only affected view data is recalculated
- **Garbage Collection**: Unused view derivations are cleaned up
- **Efficient Data Structures**: Optimized for specific view operations

## 12. Error Handling and Validation

### 12.1 View Validation

Views are validated before creation/update:

```typescript
interface IViewValidationResult {
  isValid: boolean;
  errors: IViewValidationError[];
}

interface IViewValidationError {
  field: string;                  // Problematic field
  message: string;                // Error description
  code: ViewErrorCode;            // Error type
}
```

**Common Validation Errors:**
- Invalid field references in columns
- Circular dependencies in linked fields
- Unsupported field types for view operations
- Permission violations

### 12.2 Graceful Degradation

When views encounter errors:

1. **Fallback to Grid View**: Unsupported views default to grid display
2. **Partial Rendering**: Valid portions of view are shown
3. **Error Boundaries**: React error boundaries prevent crashes
4. **User Notifications**: Clear error messages guide users

## 13. Testing and Quality Assurance

### 13.1 Unit Testing

View components and logic are thoroughly tested:

```typescript
// Example test structure
describe('GridView', () => {
  it('should generate default properties correctly', () => {
    const snapshot = createMockSnapshot();
    const properties = GridView.generateDefaultProperty(snapshot, null);
    expect(properties.type).toBe(ViewType.Grid);
    expect(properties.columns).toBeDefined();
  });
});
```

### 13.2 Integration Testing

End-to-end tests cover:
- View creation and deletion workflows
- Data filtering and sorting operations
- Cross-view data consistency
- Permission enforcement

### 13.3 Performance Testing

Regular performance benchmarks ensure:
- View derivation computation times
- Rendering performance with large datasets
- Memory usage patterns
- Network request optimization

## 14. View-Specific Implementation Details

### 14.1 Grid View Implementation

The Grid view is the most feature-rich view type, implemented using Konva.js for high-performance rendering:

<augment_code_snippet path="packages/datasheet/src/pc/components/konva_grid" mode="EXCERPT">
```typescript
// Grid view uses canvas-based rendering for performance
export const KonvaGridView: React.FC<IKonvaGridViewProps> = ({ width, height }) => {
  const { currentView, visibleRows, fieldMap } = useGridViewData();

  return (
    <Stage width={width} height={height}>
      <Layer>
        <GridHeader fields={visibleColumns} />
        <GridBody rows={visibleRows} fields={visibleColumns} />
        <GridScrollbar />
      </Layer>
    </Stage>
  );
};
```
</augment_code_snippet>

**Key Features:**
- Virtual scrolling for large datasets
- Frozen columns support
- In-cell editing capabilities
- Statistical aggregations in column footers
- Drag-and-drop column reordering

### 14.2 Kanban View Implementation

Kanban views group records into columns based on select field values:

<augment_code_snippet path="packages/datasheet/src/pc/components/kanban_view" mode="EXCERPT">
```typescript
export const KanbanView: React.FC<IKanbanViewProps> = ({ width, height }) => {
  const { groupedRecords, kanbanStyle } = useKanbanData();

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <div className="kanban-container">
        {groupedRecords.map(group => (
          <KanbanColumn
            key={group.fieldValue}
            group={group}
            style={kanbanStyle}
            onCardMove={handleCardMove}
          />
        ))}
      </div>
    </DragDropContext>
  );
};
```
</augment_code_snippet>

**Key Features:**
- Drag-and-drop card movement between columns
- Cover image display from attachment fields
- Grouping by single-select or multi-select fields
- Hidden group management
- Card count limits per column

### 14.3 Calendar View Implementation

Calendar views map records to time-based events:

<augment_code_snippet path="packages/datasheet/src/pc/components/calendar_view" mode="EXCERPT">
```typescript
export const CalendarView: React.FC<ICalendarViewProps> = ({ width, height }) => {
  const { calendarEvents, dateField } = useCalendarData();

  return (
    <FullCalendar
      plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
      initialView="dayGridMonth"
      events={calendarEvents}
      eventClick={handleEventClick}
      dateClick={handleDateClick}
      height={height}
    />
  );
};
```
</augment_code_snippet>

**Key Features:**
- Multiple calendar layouts (month, week, day)
- Event creation by clicking on dates
- Color coding based on field values
- Date range filtering
- Recurring event support

### 14.4 Gallery View Implementation

Gallery views display records as visual cards:

<augment_code_snippet path="packages/datasheet/src/pc/components/gallery_view" mode="EXCERPT">
```typescript
export const GalleryView: React.FC<IGalleryViewProps> = ({ width, height }) => {
  const { records, galleryStyle } = useGalleryData();
  const cardWidth = calculateCardWidth(width, galleryStyle.cardCount);

  return (
    <VirtualizedGrid
      width={width}
      height={height}
      columnCount={galleryStyle.cardCount}
      rowCount={Math.ceil(records.length / galleryStyle.cardCount)}
      columnWidth={cardWidth}
      rowHeight={CARD_HEIGHT}
      cellRenderer={({ columnIndex, rowIndex, style }) => (
        <GalleryCard
          record={records[rowIndex * galleryStyle.cardCount + columnIndex]}
          style={style}
          coverField={galleryStyle.coverFieldId}
        />
      )}
    />
  );
};
```
</augment_code_snippet>

**Key Features:**
- Responsive card layouts
- Cover image support with fit options
- Virtualized rendering for performance
- Customizable card content
- Filtering and sorting capabilities

## 15. Best Practices and Guidelines

### 15.1 Performance Best Practices

1. **Use Memoization**: Memoize expensive computations in view components
```typescript
const memoizedViewData = useMemo(() => {
  return processViewData(rawData, viewConfig);
}, [rawData, viewConfig]);
```

2. **Implement Virtual Scrolling**: For large datasets, use virtual scrolling
```typescript
// Use react-window or similar libraries
<FixedSizeList
  height={height}
  itemCount={items.length}
  itemSize={ITEM_HEIGHT}
  itemData={items}
>
  {Row}
</FixedSizeList>
```

3. **Optimize Re-renders**: Use React.memo and useCallback appropriately
```typescript
const ViewComponent = React.memo(({ data, onUpdate }) => {
  const handleUpdate = useCallback((newData) => {
    onUpdate(newData);
  }, [onUpdate]);

  return <div>{/* component content */}</div>;
});
```

### 15.2 State Management Best Practices

1. **Normalize State Structure**: Keep view data normalized
```typescript
interface IViewState {
  byId: { [viewId: string]: IViewProperty };
  allIds: string[];
  activeViewId: string | null;
}
```

2. **Use Selectors**: Create reusable selectors for common data access patterns
```typescript
const getVisibleRecords = createSelector(
  [getRecords, getCurrentView],
  (records, view) => applyViewFilters(records, view.filterInfo)
);
```

3. **Batch Updates**: Group related state updates
```typescript
dispatch(batchActions([
  setViewFilter(viewId, filterInfo),
  setViewSort(viewId, sortInfo),
  refreshViewDerivation(viewId)
]));
```

### 15.3 Error Handling Best Practices

1. **Graceful Degradation**: Provide fallbacks for unsupported features
```typescript
const ViewRenderer = ({ viewType, ...props }) => {
  try {
    return getViewComponent(viewType)(props);
  } catch (error) {
    console.error('View rendering error:', error);
    return <GridView {...props} />; // Fallback to grid
  }
};
```

2. **User-Friendly Error Messages**: Show clear error messages
```typescript
const ErrorBoundary = ({ children }) => {
  return (
    <ErrorBoundary
      fallback={<div>Something went wrong with this view. Please try refreshing.</div>}
    >
      {children}
    </ErrorBoundary>
  );
};
```

### 15.4 Testing Best Practices

1. **Test View Logic Separately**: Unit test view derivation logic
```typescript
describe('ViewDerivateGrid', () => {
  it('should filter records correctly', () => {
    const derivate = new ViewDerivateGrid(mockState, datasheetId);
    const filtered = derivate.applyFilters(mockRecords, mockFilterInfo);
    expect(filtered).toHaveLength(expectedCount);
  });
});
```

2. **Mock External Dependencies**: Mock complex dependencies in tests
```typescript
jest.mock('../../selectors', () => ({
  getCurrentView: jest.fn(() => mockView),
  getVisibleRows: jest.fn(() => mockRows)
}));
```

3. **Test User Interactions**: Test drag-and-drop and other interactions
```typescript
it('should handle card drag and drop', async () => {
  render(<KanbanView {...props} />);
  const card = screen.getByTestId('kanban-card-1');
  const column = screen.getByTestId('kanban-column-2');

  await dragAndDrop(card, column);
  expect(mockOnCardMove).toHaveBeenCalledWith(cardId, newColumnId);
});
```

This comprehensive documentation covers all aspects of the APITable view system, from basic architecture to advanced implementation details and best practices. The system's robust design enables powerful data visualization while maintaining performance and extensibility.
