/*
 * APITable Ltd. <<EMAIL>>
 * Copyright (C)  2022 APITable Ltd. <https://apitable.com>
 *
 * This code file is part of APITable Enterprise Edition.
 *
 * It is subject to the APITable Commercial License
 *  and conditional on having a fully paid-up license from APITable.
 *
 * Access to this code file or other code files in this `enterprise` directory
 * and its subdirectories does not constitute permission to use this code
 * or APITable Enterprise Edition features.
 *
 * Unless otherwise noted, all files Copyright © 2022 APITable Ltd.
 *
 * For purchase of APITable Enterprise Edition license, please contact <<EMAIL>>.
 */

package com.apitable.interfaces.security.facade;

/**
 * default black list service facade implement.
 */
public class DefaultWhiteListServiceFacadeImpl implements WhiteListServiceFacade {

    @Override
    public void checkWidgetPermission(Long userId) {
    }
}
